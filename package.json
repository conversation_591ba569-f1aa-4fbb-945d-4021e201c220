{"name": "airstar", "scripts": {"prepare": "husky install", "start:planner": "sh planner-dev.sh", "start:server:force": "turbo run start --filter=@airstar/server-ai --force", "start:server": "turbo run start --filter=@airstar-ai/server", "knip": "knip", "knip:production": "knip --production", "knip:unused-files": "knip --include files", "knip:unused-deps": "knip --include dependencies"}, "lint-staged": {"apps/**/*.{ts, tsx}": "eslint --fix", "packages/**/*.{ts, tsx}": "eslint --fix"}, "workspaces": ["apps/*", "packages/*"], "devDependencies": {"husky": "^7.0.4", "lint-staged": "^13.2.3", "turbo": "^1.6.3", "yaml": "^2.1.1"}, "engines": {"node": ">=16 <19"}, "pnpm": {"executionEnv": {"nodeVersion": "18.20.4"}}, "dependencies": {"zx": "~6.0.7"}}