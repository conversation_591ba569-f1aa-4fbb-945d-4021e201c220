// 前后端消息协议定义 - 后端版本
// 与前端 apps/client-planner/src/service/api/types.ts 保持一致

export interface Option {
  text: string;
  value: string;
}

export interface ToolCall {
  type: 'tool_call';
  id: string;
  name: string;
  args: Record<string, unknown>;
}

export interface ToolCallChunk {
  type: 'tool_call_chunk';
  index: number;
  id: string;
  name: string;
  args: string;
}

// 基础事件数据接口
interface BaseEventData {
  id: string;
  threadId: string;
  agent:
    | 'coordinator'
    | 'planner'
    | 'researcher'
    | 'coder'
    | 'reporter'
    | 'analyst'
    | 'podcast';
  role: 'user' | 'assistant' | 'tool';
  timestamp?: string;
  finish_reason?: 'stop' | 'tool_calls' | 'interrupt' | 'error';
}

interface GenericEvent<T extends string, D extends object = {}> {
  type: T;
  data: BaseEventData & D;
}

// 1. 普通文档消息：逐个传递文本信息
export interface MessageChunkEvent
  extends GenericEvent<
    'message_chunk',
    {
      content?: string;
      delta?: string; // 增量内容
      isComplete?: boolean; // 是否完成
    }
  > {}

// 2. 工具调用相关消息
export interface ToolCallsEvent
  extends GenericEvent<
    'tool_calls',
    {
      tool_calls: ToolCall[];
      tool_call_chunks: ToolCallChunk[];
    }
  > {}

export interface ToolCallChunksEvent
  extends GenericEvent<
    'tool_call_chunks',
    {
      tool_call_chunks: ToolCallChunk[];
    }
  > {}

export interface ToolCallResultEvent
  extends GenericEvent<
    'tool_call_result',
    {
      tool_call_id: string;
      content?: string;
      success?: boolean;
      error?: string;
    }
  > {}

// 3. 中断消息：需要用户反馈
export interface InterruptEvent
  extends GenericEvent<
    'interrupt',
    {
      options: Option[];
      message?: string; // 中断说明
      context?: Record<string, any>; // 中断上下文
    }
  > {}

// 4. 任务状态相关消息
export interface TaskStatusEvent
  extends GenericEvent<
    'task_status',
    {
      status: 'started' | 'in_progress' | 'completed' | 'failed' | 'paused';
      taskId?: string;
      taskName?: string;
      progress?: number; // 0-100
      message?: string;
      metadata?: Record<string, any>;
    }
  > {}

// 5. 系统状态消息
export interface SystemStatusEvent
  extends GenericEvent<
    'system_status',
    {
      status: 'connecting' | 'connected' | 'disconnected' | 'error' | 'ready';
      message?: string;
      error?: string;
    }
  > {}

// 6. 错误消息
export interface ErrorEvent
  extends GenericEvent<
    'error',
    {
      error: string;
      code?: string;
      details?: Record<string, any>;
      recoverable?: boolean;
    }
  > {}

// 7. 会话结束消息
export interface EndEvent
  extends GenericEvent<
    'end',
    {
      reason: 'completed' | 'cancelled' | 'error' | 'timeout';
      summary?: string;
      results?: Record<string, any>;
      nextActions?: string[];
    }
  > {}

// 8. 进度更新消息
export interface ProgressEvent
  extends GenericEvent<
    'progress',
    {
      step: string;
      current: number;
      total: number;
      percentage: number;
      estimatedTimeRemaining?: number; // 秒
      message?: string;
    }
  > {}

// 9. 文件/资源相关消息
export interface ResourceEvent
  extends GenericEvent<
    'resource',
    {
      action: 'created' | 'updated' | 'deleted' | 'uploaded' | 'downloaded';
      resourceType: 'file' | 'image' | 'document' | 'code' | 'data';
      resourceId: string;
      resourceName?: string;
      resourceUrl?: string;
      metadata?: Record<string, any>;
    }
  > {}

// 10. 通知消息
export interface NotificationEvent
  extends GenericEvent<
    'notification',
    {
      level: 'info' | 'warning' | 'error' | 'success';
      title: string;
      message: string;
      actionable?: boolean;
      actions?: Array<{
        label: string;
        action: string;
        data?: Record<string, any>;
      }>;
    }
  > {}

export type ChatEvent =
  | MessageChunkEvent
  | ToolCallsEvent
  | ToolCallChunksEvent
  | ToolCallResultEvent
  | InterruptEvent
  | TaskStatusEvent
  | SystemStatusEvent
  | ErrorEvent
  | EndEvent
  | ProgressEvent
  | ResourceEvent
  | NotificationEvent;

// 事件创建工具函数
export class ChatEventBuilder {
  static messageChunk(
    data: Omit<MessageChunkEvent['data'], 'id' | 'timestamp'>,
  ): MessageChunkEvent {
    return {
      type: 'message_chunk',
      data: {
        ...data,
        id: this.generateId(),
        timestamp: new Date().toISOString(),
      },
    };
  }

  static toolCalls(
    data: Omit<ToolCallsEvent['data'], 'id' | 'timestamp'>,
  ): ToolCallsEvent {
    return {
      type: 'tool_calls',
      data: {
        ...data,
        id: this.generateId(),
        timestamp: new Date().toISOString(),
      },
    };
  }

  static interrupt(
    data: Omit<InterruptEvent['data'], 'id' | 'timestamp'>,
  ): InterruptEvent {
    return {
      type: 'interrupt',
      data: {
        ...data,
        id: this.generateId(),
        timestamp: new Date().toISOString(),
      },
    };
  }

  static taskStatus(
    data: Omit<TaskStatusEvent['data'], 'id' | 'timestamp'>,
  ): TaskStatusEvent {
    return {
      type: 'task_status',
      data: {
        ...data,
        id: this.generateId(),
        timestamp: new Date().toISOString(),
      },
    };
  }

  static end(data: Omit<EndEvent['data'], 'id' | 'timestamp'>): EndEvent {
    return {
      type: 'end',
      data: {
        ...data,
        id: this.generateId(),
        timestamp: new Date().toISOString(),
      },
    };
  }

  static error(data: Omit<ErrorEvent['data'], 'id' | 'timestamp'>): ErrorEvent {
    return {
      type: 'error',
      data: {
        ...data,
        id: this.generateId(),
        timestamp: new Date().toISOString(),
      },
    };
  }

  static progress(
    data: Omit<ProgressEvent['data'], 'id' | 'timestamp'>,
  ): ProgressEvent {
    return {
      type: 'progress',
      data: {
        ...data,
        id: this.generateId(),
        timestamp: new Date().toISOString(),
      },
    };
  }

  static notification(
    data: Omit<NotificationEvent['data'], 'id' | 'timestamp'>,
  ): NotificationEvent {
    return {
      type: 'notification',
      data: {
        ...data,
        id: this.generateId(),
        timestamp: new Date().toISOString(),
      },
    };
  }

  private static generateId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }
}
