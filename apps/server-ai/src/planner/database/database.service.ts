import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository, DataSource } from 'typeorm';
import { Message } from './entities/message.entity';
import { Session } from './entities/session.entity';
import { Task } from './entities/task.entity';
import { PRD } from './entities/prd.entity';

@Injectable()
export class DatabaseService {
  constructor(
    @InjectRepository(Message)
    private readonly messageRepository: Repository<Message>,
    @InjectRepository(Session)
    private readonly sessionRepository: Repository<Session>,
    @InjectRepository(Task)
    private readonly taskRepository: Repository<Task>,
    @InjectRepository(PRD)
    private readonly prdRepository: Repository<PRD>,
    private readonly dataSource: DataSource,
  ) {}

  // Message methods
  async createMessage(messageData: Partial<Message>): Promise<Message> {
    const message = this.messageRepository.create(messageData);
    return this.messageRepository.save(message);
  }

  async findMessageById(id: string): Promise<Message> {
    return this.messageRepository.findOne({ where: { id } });
  }

  async findMessagesByThreadId(threadId: string): Promise<Message[]> {
    return this.messageRepository.find({
      where: { threadId },
      order: { createdAt: 'ASC' }, // Order messages by creation time
    });
  }

  async findAllMessages(): Promise<Message[]> {
    return this.messageRepository.find();
  }

  async deleteMessage(id: string): Promise<void> {
    await this.messageRepository.delete(id);
  }

  // Session workflow methods (merged from Workflow)
  async createOrUpdateSessionByThreadId(
    sessionData: Partial<Session>,
  ): Promise<Session> {
    const existingSession = await this.findSessionByThreadId(
      sessionData.threadId,
    );
    if (existingSession) {
      await this.sessionRepository.update(existingSession.id, sessionData);
      return this.findSessionById(existingSession.id);
    } else {
      const session = this.sessionRepository.create(sessionData);
      return this.sessionRepository.save(session);
    }
  }

  async updateSession(
    id: string,
    sessionData: Partial<Session>,
  ): Promise<Session> {
    await this.sessionRepository.update(id, sessionData);
    return this.findSessionById(id);
  }

  // Session methods
  async createSession(sessionData: Partial<Session>): Promise<Session> {
    const session = this.sessionRepository.create(sessionData);
    return this.sessionRepository.save(session);
  }

  async findSessionById(id: string): Promise<Session> {
    return this.sessionRepository.findOne({ where: { id, deletedAt: null } });
  }

  async findSessionByThreadId(threadId: string): Promise<Session> {
    return this.sessionRepository.findOne({
      where: { threadId, deletedAt: null },
    });
  }

  // Task methods
  async findTaskById(id: string): Promise<Task> {
    return this.taskRepository.findOne({ where: { id } });
  }

  async findTasksBySessionId(sessionId: string): Promise<Task[]> {
    return this.taskRepository.find({ where: { session: { id: sessionId } } });
  }

  async saveTask(task: Partial<Task>): Promise<Task> {
    // If task.id exists, TypeORM's save acts like an update.
    // If task.id doesn't exist (or is undefined), it acts like a create.
    // For updating status, we expect task to be an existing entity fetched prior.
    return this.taskRepository.save(task);
  }

  async findTaskByIdAndSessionId(
    taskId: string,
    sessionId: string,
  ): Promise<Task> {
    return this.taskRepository.findOne({
      where: { id: taskId, session: { id: sessionId } },
      relations: ['session'],
    });
  }

  // PRD methods
  async createPRD(prdData: Partial<PRD>): Promise<PRD> {
    const prd = this.prdRepository.create(prdData);
    return this.prdRepository.save(prd);
  }

  async findPRDById(id: string): Promise<PRD> {
    return this.prdRepository.findOne({
      where: { id },
      relations: ['session'],
    });
  }

  async findPRDsBySessionId(sessionId: string): Promise<PRD[]> {
    return this.prdRepository.find({
      where: { session: { id: sessionId } },
      relations: ['session'],
      order: { version: 'DESC' },
    });
  }

  async findLatestPRDBySessionId(sessionId: string): Promise<PRD> {
    return this.prdRepository.findOne({
      where: { session: { id: sessionId } },
      relations: ['session'],
      order: { version: 'DESC' },
    });
  }

  async updatePRD(id: string, prdData: Partial<PRD>): Promise<PRD> {
    await this.prdRepository.update(id, prdData);
    return this.findPRDById(id);
  }

  // Transaction methods
  async runInTransaction<T>(
    operation: (manager: any) => Promise<T>,
  ): Promise<T> {
    return this.dataSource.transaction(operation);
  }

  // Batch operations
  async createSessionWithPRD(
    sessionData: Partial<Session>,
    prdData: Partial<PRD>,
  ): Promise<{ session: Session; prd: PRD }> {
    return this.runInTransaction(async (manager) => {
      const session = manager.create(Session, sessionData);
      const savedSession = await manager.save(session);

      const prd = manager.create(PRD, {
        ...prdData,
        session: savedSession,
      });
      const savedPRD = await manager.save(prd);

      return { session: savedSession, prd: savedPRD };
    });
  }

  async createTasksForSession(
    sessionId: string,
    tasksData: Partial<Task>[],
  ): Promise<Task[]> {
    return this.runInTransaction(async (manager) => {
      const session = await manager.findOne(Session, {
        where: { id: sessionId },
      });
      if (!session) {
        throw new Error(`Session with id ${sessionId} not found`);
      }

      const tasks = tasksData.map((taskData, index) =>
        manager.create(Task, {
          ...taskData,
          session,
          order: taskData.order ?? index,
        }),
      );

      return manager.save(tasks);
    });
  }

  // Advanced query methods
  async findSessionsWithStats(): Promise<any[]> {
    return this.sessionRepository
      .createQueryBuilder('session')
      .leftJoinAndSelect('session.tasks', 'task')
      .leftJoinAndSelect('session.prds', 'prd')
      .where('session.deletedAt IS NULL') // 只查询未删除的记录
      .loadRelationCountAndMap('session.taskCount', 'session.tasks')
      .loadRelationCountAndMap('session.prdCount', 'session.prds')
      .orderBy('session.createdAt', 'DESC')
      .getMany();
  }

  async findSessionByThreadIdWithRelations(threadId: string): Promise<Session> {
    return this.sessionRepository.findOne({
      where: { threadId, deletedAt: null },
      relations: ['tasks', 'prds'],
      order: {
        tasks: { order: 'ASC' },
        prds: { version: 'DESC' },
      },
    });
  }

  /**
   * 查找最近创建的没有threadId的会话
   */
  async findRecentSessionsWithoutThreadId(
    minutesAgo: number = 5,
  ): Promise<Session[]> {
    const timeThreshold = new Date(Date.now() - minutesAgo * 60 * 1000);

    return this.sessionRepository
      .createQueryBuilder('session')
      .where('session.threadId IS NULL')
      .andWhere('session.deletedAt IS NULL')
      .andWhere('session.createdAt >= :timeThreshold', { timeThreshold })
      .orderBy('session.createdAt', 'DESC')
      .limit(5) // 最多返回5个会话
      .getMany();
  }
}
